import { RefObject, useCallback, useEffect, useState } from 'react';

interface UseSimpleTextEditorProps {
  containerRef: RefObject<HTMLElement | null>;
  onContentChange?: (html: string) => void;
}

export const useSimpleTextEditor = ({
  containerRef,
  onContentChange
}: UseSimpleTextEditorProps) => {
  const [isEditMode, setIsEditMode] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  const handleTextInput = useCallback(() => {
    setHasChanges(true);
  }, []);

  const handleTextChange = useCallback(() => {
    if (containerRef.current && onContentChange) {
      const updatedHtml = containerRef.current.innerHTML;
      onContentChange(updatedHtml);
    }
  }, [containerRef, onContentChange]);

  const makeTextEditable = useCallback(() => {
    if (!containerRef.current) return;

    // Znajdź wszystkie elementy tekstowe
    const textSelectors = 'h1, h2, h3, h4, h5, h6, p, li, span:not([class*="icon"]):not([class*="sr-"]), div:not([class*="container"]):not([class*="wrapper"]):not([class*="flex"]):not([class*="grid"])';
    const textElements = containerRef.current.querySelectorAll(textSelectors);

    textElements.forEach((element) => {
      const htmlElement = element as HTMLElement;
      
      // Sprawdź czy element ma text content (nie jest pusty ani nie zawiera tylko elementów)
      if (htmlElement.textContent?.trim()) {
        htmlElement.setAttribute('contenteditable', 'true');
        htmlElement.classList.add('editable-text');
        
        // Dodaj event listener dla zmian
        htmlElement.addEventListener('blur', handleTextChange);
        htmlElement.addEventListener('input', handleTextInput);
      }
    });
  }, [containerRef, handleTextChange, handleTextInput]);

  const disableTextEditing = useCallback(() => {
    if (!containerRef.current) return;

    const editableElements = containerRef.current.querySelectorAll('.editable-text');
    editableElements.forEach((element) => {
      const htmlElement = element as HTMLElement;
      htmlElement.removeAttribute('contenteditable');
      htmlElement.classList.remove('editable-text');
      htmlElement.removeEventListener('blur', handleTextChange);
      htmlElement.removeEventListener('input', handleTextInput);
    });
  }, [containerRef, handleTextChange, handleTextInput]);

  const enableEditMode = useCallback(() => {
    setIsEditMode(true);
    setTimeout(makeTextEditable, 100); // Małe opóźnienie dla pewności
  }, [makeTextEditable]);

  const disableEditMode = useCallback(() => {
    setIsEditMode(false);
    disableTextEditing();
    setHasChanges(false);
  }, [disableTextEditing]);

  // Cleanup przy unmount
  useEffect(() => {
    return () => {
      disableTextEditing();
    };
  }, [disableTextEditing]);

  return {
    isEditMode,
    hasChanges,
    enableEditMode,
    disableEditMode,
    makeTextEditable,
    disableTextEditing
  };
};